import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    name: 'integration',
    include: ['src/__tests__/integration/**/*.test.ts'],
    exclude: ['src/__tests__/unit/**/*', 'src/__tests__/*.test.ts', 'node_modules/**/*'],
    environment: 'node', // Use node environment for integration tests
    globals: true,
    globalSetup: ['./src/__tests__/integration/global-setup.ts'],
    testTimeout: 60000, // 60 seconds for integration tests (database + API operations)
    hookTimeout: 120000, // 2 minutes for setup/teardown (database seeding + server startup)
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true, // Run integration tests sequentially
      },
    },
    env: {
      NODE_ENV: 'test',
      TEST_MODE: 'integration',
      // Use the real PostgreSQL database
      DATABASE_URL: 'postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens',
      CACHE_TYPE: 'postgresql',
      SESSION_SECRET: 'test-session-secret-for-integration-tests',
      USE_LOCAL_AUTH: 'true',
      // Disable rate limiting for integration tests
      DISABLE_RATE_LIMITING: 'true',
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  esbuild: {
    jsx: 'automatic',
    jsxDev: true,
  },
})
